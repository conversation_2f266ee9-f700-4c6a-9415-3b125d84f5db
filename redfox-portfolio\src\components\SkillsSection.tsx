import { useEffect, useRef, useState } from 'react';

interface Skill {
  name: string;
  level: number;
}

const skills: Skill[] = [
  { name: "IA Générative", level: 95 },
  { name: "Machine Learning", level: 90 },
  { name: "Python", level: 85 },
  { name: "JavaScript", level: 80 },
  { name: "Pixel Art", level: 75 }
];

export function SkillsSection() {
  const [animatedSkills, setAnimatedSkills] = useState<boolean[]>(new Array(skills.length).fill(false));
  const sectionRef = useRef<HTMLElement>(null);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            // Animate skills with staggered delays
            skills.forEach((_, index) => {
              setTimeout(() => {
                setAnimatedSkills(prev => {
                  const newState = [...prev];
                  newState[index] = true;
                  return newState;
                });
              }, index * 200);
            });
          }
        });
      },
      { threshold: 0.85 }
    );

    if (sectionRef.current) {
      observer.observe(sectionRef.current);
    }

    return () => observer.disconnect();
  }, []);

  return (
    <section
      ref={sectionRef}
      id="competences"
      className="section"
    >
      <h2 className="text-2xl text-white mb-2">Compétences</h2>
      
      <div className="flex flex-col gap-5 mt-6">
        {skills.map((skill, index) => (
          <div key={skill.name} className="flex items-center gap-5">
            <span className="min-w-[140px] font-bold text-orange text-shadow-[1px_1px_0_var(--black)]">
              {skill.name}
            </span>
            
            <div className="flex-1 h-5 bg-gray-medium border-2 border-orange-light rounded-none overflow-hidden relative shadow-[0_2px_0_var(--black)]">
              <div
                className={`
                  h-full bg-gradient-to-r from-orange to-orange-light rounded-none absolute left-0 top-0
                  transition-all duration-1000 ease-out
                  ${animatedSkills[index] ? `w-[${skill.level}%]` : 'w-0'}
                `}
                style={{
                  width: animatedSkills[index] ? `${skill.level}%` : '0%',
                  transitionDelay: `${index * 200}ms`
                }}
              />
            </div>
          </div>
        ))}
      </div>
    </section>
  );
}

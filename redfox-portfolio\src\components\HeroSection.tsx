import { useEffect } from 'react';

export function HeroSection() {
  useEffect(() => {
    // Add the fox blink animation to the CSS
    const style = document.createElement('style');
    style.textContent = `
      @keyframes fox-blink {
        0%, 92%, 100% { opacity: 1; }
        94%, 98% { opacity: 0.7; }
      }
      .fox-blink {
        animation: fox-blink 4s infinite;
      }
    `;
    document.head.appendChild(style);

    return () => {
      document.head.removeChild(style);
    };
  }, []);

  return (
    <section id="accueil" className="section">
      <div className="flex flex-col items-center gap-5">
        <div className="mb-2">
          <img
            src="/images/fox_transparent.png"
            alt="Logo RedFox"
            className="max-w-[120px] mt-3 fox-blink"
          />
        </div>

        <h1 className="text-5xl text-orange tracking-wider m-0 font-bold" style={{ textShadow: '1px 1px 0 var(--black)' }}>
          RedFox
        </h1>

        <p className="text-lg text-orange-light bg-gray-medium px-4 py-2 rounded-md shadow-none italic">
          Comprendre, développer et travailler avec l'IA : dans tous vos métiers.
        </p>
      </div>

      {/* Mini carte d'identité */}
      <div className="flex items-center bg-gray-medium border-2 border-orange-light rounded-sm shadow-[0_2px_8px_rgba(0,0,0,0.1)] max-w-md mx-auto mt-9 mb-9 px-5 py-3 gap-4">
        <span
          className="inline-block w-8 h-6 rounded-sm border border-gray-400 flex-shrink-0"
          style={{
            background: 'linear-gradient(90deg, #0055a4 33%, #fff 33% 66%, #ef4135 66%)'
          }}
          aria-label="Français"
        />

        <div className="flex flex-col gap-0.5">
          <span className="text-orange font-bold text-lg">
            Jérémy M.
          </span>
          <span className="text-white text-base">
            Développeur polymorphe <br />
            Consultant-Expert en IA Générative <br />
            Intégrateur Cloud (spé. Azure)
          </span>
          <span className="text-orange-light text-sm italic">
            Région Grand Ouest, France
          </span>
        </div>
      </div>
    </section>
  );
}

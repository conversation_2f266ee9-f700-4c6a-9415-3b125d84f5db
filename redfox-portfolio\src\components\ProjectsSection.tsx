import { useState, useMemo } from 'react';
import { Button } from './ui/button';

interface Project {
  id: string;
  title: string;
  description: string;
  tags: string[];
  searchText: string;
  startDate: string;
  endDate: string;
  image?: string;
}

const projects: Project[] = [
  {
    id: "foxgen",
    title: "FoxGen",
    description: "Générateur d'images IA inspiré du pixel art et de la faune sauvage.",
    tags: ["Python", "IA Générative", "Pixel Art"],
    searchText: "foxgen générateur images ia pixel art faune sauvage",
    startDate: "01/2024",
    endDate: "03/2024"
  },
  {
    id: "text2quest",
    title: "Text2Quest",
    description: "Création d'aventures textuelles interactives grâce à l'IA générative.",
    tags: ["JavaScript", "IA Générative"],
    searchText: "text2quest aventures textuelles interactives ia générative jeu",
    startDate: "02/2024",
    endDate: "05/2024"
  },
  {
    id: "retrosynth",
    title: "RetroSynth",
    description: "Synthétiseur de sons rétro piloté par IA pour jeux vidéo pixel art.",
    tags: ["JavaScript", "Web Audio API", "IA Générative"],
    searchText: "retrosynth synthétiseur sons rétro ia jeux vidéo pixel art audio",
    startDate: "04/2024",
    endDate: "En cours"
  }
];

type ViewMode = 'detailed' | 'compact';

export function ProjectsSection() {
  const [searchTerm, setSearchTerm] = useState('');
  const [viewMode, setViewMode] = useState<ViewMode>('detailed');
  const [expandedProject, setExpandedProject] = useState<string | null>(null);

  // Normalize text for search
  const normalizeText = (text: string) => {
    return text.toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '');
  };

  // Filter projects based on search term
  const filteredProjects = useMemo(() => {
    if (!searchTerm.trim()) return projects;
    
    const normalizedSearch = normalizeText(searchTerm);
    return projects.filter(project => 
      normalizeText(project.searchText).includes(normalizedSearch)
    );
  }, [searchTerm]);

  const toggleProject = (projectId: string) => {
    if (viewMode === 'compact') return; // No expansion in compact view
    
    setExpandedProject(expandedProject === projectId ? null : projectId);
  };

  const toggleViewMode = () => {
    const newMode = viewMode === 'detailed' ? 'compact' : 'detailed';
    setViewMode(newMode);
    setExpandedProject(null); // Close any expanded projects
    localStorage.setItem('projectsViewMode', newMode);
  };

  // Load saved view mode on component mount
  useEffect(() => {
    const savedMode = localStorage.getItem('projectsViewMode') as ViewMode;
    if (savedMode) {
      setViewMode(savedMode);
    }
  }, []);

  return (
    <section id="projets" className="section">
      <h2 className="text-2xl text-white mb-2">Projets</h2>

      {/* Project Filters */}
      <div className="bg-gray-medium border-2 border-orange rounded-none p-5 mb-8 shadow-[0_2px_0_var(--black)]">
        <div className="mb-4">
          <label htmlFor="project-search" className="sr-only">
            Rechercher un projet
          </label>
          <input
            type="text"
            id="project-search"
            className="w-full max-w-md font-mono text-base border-2 border-gray-dark rounded-none px-4 py-2 bg-black text-white outline-none transition-colors duration-200 focus:border-orange-light"
            placeholder="Rechercher un projet..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            aria-describedby="search-help"
          />
          <span id="search-help" className="sr-only">
            Tapez pour filtrer les projets par nom ou description
          </span>
        </div>

        <div className="flex justify-between items-center mt-4 flex-wrap gap-4">
          <div className="flex-1 text-left">
            <span className="text-orange-light italic text-sm" aria-live="polite" aria-atomic="true">
              {filteredProjects.length} projet{filteredProjects.length !== 1 ? 's' : ''} affiché{filteredProjects.length !== 1 ? 's' : ''}
            </span>
          </div>

          <div className="flex items-center gap-2">
            <span className="text-orange-light font-bold text-sm">Affichage :</span>
            <Button
              onClick={toggleViewMode}
              className={`
                flex items-center gap-2 bg-gray-dark text-white border-2 border-orange rounded-none px-3 py-2
                font-mono text-sm cursor-pointer transition-all duration-200 font-bold
                hover:bg-orange hover:text-black hover:border-orange-light
                ${viewMode === 'compact' ? 'bg-orange text-black border-orange-light shadow-[0_2px_0_var(--black)]' : ''}
              `}
              aria-pressed={viewMode === 'compact'}
              aria-label="Basculer entre vue détaillée et vue compacte"
              title="Cliquez pour changer le mode d'affichage"
            >
              <span className="text-base leading-none">
                {viewMode === 'detailed' ? '📋' : '📊'}
              </span>
              <span className="text-xs">
                {viewMode === 'detailed' ? 'Détaillé' : 'Compact'}
              </span>
            </Button>
          </div>
        </div>
      </div>

      {/* Projects List */}
      <div className={`
        ${viewMode === 'compact' 
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-8' 
          : 'flex flex-col gap-6 mt-8 w-full'
        }
      `}>
        {filteredProjects.map((project) => (
          <ProjectItem
            key={project.id}
            project={project}
            viewMode={viewMode}
            isExpanded={expandedProject === project.id}
            onToggle={() => toggleProject(project.id)}
          />
        ))}
      </div>
    </section>
  );
}

interface ProjectItemProps {
  project: Project;
  viewMode: ViewMode;
  isExpanded: boolean;
  onToggle: () => void;
}

function ProjectItem({ project, viewMode, isExpanded, onToggle }: ProjectItemProps) {
  if (viewMode === 'compact') {
    return (
      <div className="bg-gray-medium border-2 border-orange rounded-none p-3 transition-all duration-300 cursor-pointer min-w-0 box-border hover:border-orange-light hover:shadow-[0_2px_4px_rgba(0,0,0,0.1)]">
        <div className="flex items-center mb-2">
          <div className="w-6 h-6 bg-orange border border-orange-light rounded-none mr-3 mb-2 flex-shrink-0" />
          <h3 className="text-lg mb-2 text-orange font-bold">{project.title}</h3>
        </div>
        
        <p className="text-sm text-white mb-3 leading-relaxed break-words">
          <i>{project.description}</i>
        </p>
        
        <div className="flex gap-4 mb-3 text-xs">
          <span className="flex gap-1">
            <span className="text-orange-light font-bold min-w-0">Début:</span>
            <span className="text-white">{project.startDate}</span>
          </span>
          <span className="flex gap-1">
            <span className="text-orange-light font-bold min-w-0">Fin:</span>
            <span className="text-white">{project.endDate}</span>
          </span>
        </div>
        
        <button className="text-xs px-2 py-1 bg-orange text-black border-none rounded font-bold transition-colors duration-300 hover:bg-orange-light">
          + d'infos
        </button>
      </div>
    );
  }

  return (
    <div className="flex flex-col w-full transition-all duration-300 ease-in-out">
      <div
        className={`
          flex items-center bg-gray-medium border-2 border-orange rounded-none px-4 py-3 cursor-pointer
          relative z-10 transition-all duration-200
          hover:border-orange-light
          ${isExpanded ? 'bg-gray-dark border-orange-light' : ''}
        `}
        onClick={onToggle}
        role="button"
        aria-expanded={isExpanded}
        aria-controls={`project-${project.id}`}
        tabIndex={0}
        onKeyDown={(e) => {
          if (e.key === 'Enter' || e.key === ' ') {
            e.preventDefault();
            onToggle();
          }
        }}
      >
        <div className="w-9 h-9 bg-orange border border-orange-light rounded-none mr-4 flex-shrink-0" />
        <h3 className="text-orange m-0 text-lg flex-grow">{project.title}</h3>
        <span className={`
          text-orange-light text-sm transition-transform duration-200 ease-in-out block
          ${isExpanded ? 'rotate-90' : ''}
        `}>
          ▶
        </span>
      </div>

      {/* Project Details */}
      <div className={`
        flex bg-gray-dark border-2 border-orange border-t-0 px-5 py-0 gap-6 overflow-hidden
        transition-all duration-250 ease-out
        ${isExpanded
          ? 'opacity-100 max-h-80 py-5 border-t-2 border-t-orange-light'
          : 'opacity-0 max-h-0'
        }
      `} id={`project-${project.id}`}>
        <div className="w-32 h-32 bg-orange border-2 border-orange-light flex-shrink-0" />
        
        <div className="flex-grow">
          <h3 className="text-orange m-0 mb-3 text-xl">{project.title}</h3>
          <p className="text-white text-base mb-5">
            <i>{project.description}</i>
          </p>
          
          <div className="flex flex-col gap-2">
            <span className="flex gap-2">
              <span className="text-orange-light font-bold font-mono min-w-[60px]">Début:</span>
              <span className="text-white font-mono tracking-wide">{project.startDate}</span>
            </span>
            <span className="flex gap-2">
              <span className="text-orange-light font-bold font-mono min-w-[60px]">Fin:</span>
              <span className="text-white font-mono tracking-wide">{project.endDate}</span>
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}

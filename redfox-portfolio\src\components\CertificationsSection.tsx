interface Certification {
  id: string;
  title: string;
  organization: string;
  year: string;
  badge: string;
}

const certifications: Certification[] = [
  {
    id: "ai-102",
    title: "AI-102 / AI Engineer Associate",
    organization: "Microsoft",
    year: "2025",
    badge: "/images/microsoft-certified-associate-badge.png"
  },
  {
    id: "az-900",
    title: "AZ-900 / Azure Fundamentals",
    organization: "Microsoft",
    year: "2024",
    badge: "/images/microsoft-certified-fundamentals-badge.png"
  }
];

export function CertificationsSection() {
  return (
    <section id="certifications" className="section">
      <h2 className="text-2xl text-white mb-2">Certifications</h2>
      
      <div className="flex flex-wrap gap-6 justify-center mt-6">
        {certifications.map((cert) => (
          <div
            key={cert.id}
            className="flex items-center gap-4 bg-gray-medium px-4 py-4 rounded-none shadow-[0_2px_0_var(--black)] border-2 border-orange transition-all duration-300 ease-in-out hover:-translate-y-1 hover:shadow-[0_6px_12px_rgba(0,0,0,0.15)] flex-[0_1_calc(50%-1rem)] max-w-md"
          >
            <img
              src={cert.badge}
              alt={`Badge ${cert.title}`}
              className="max-w-[60px] h-auto object-contain flex-shrink-0"
            />
            <p className="m-0 text-white flex-grow">
              <strong>{cert.title}</strong> {cert.organization} - {cert.year}
            </p>
          </div>
        ))}
      </div>
    </section>
  );
}

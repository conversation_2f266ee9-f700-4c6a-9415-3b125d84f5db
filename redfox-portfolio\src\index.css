@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));
@custom-variant light (&:is(.light *));

/* RedFox Portfolio Pixel Art Theme */
:root {
  /* Original shadcn/ui variables */
  --radius: 0;

  /* RedFox Pixel Art Color Palette */
  --orange: #ff7f2a;
  --orange-light: #ff9900;
  --gray-dark: #222;
  --gray-medium: #444;
  --black: #000;
  --white: #f5f5f5;
  --accent: #fff;

  /* Shadcn/ui theme mapping to RedFox colors */
  --background: var(--gray-dark);
  --foreground: var(--white);
  --card: var(--gray-medium);
  --card-foreground: var(--white);
  --popover: var(--gray-medium);
  --popover-foreground: var(--white);
  --primary: var(--orange);
  --primary-foreground: var(--black);
  --secondary: var(--gray-medium);
  --secondary-foreground: var(--white);
  --muted: var(--gray-medium);
  --muted-foreground: var(--orange-light);
  --accent: var(--orange-light);
  --accent-foreground: var(--black);
  --destructive: #dc2626;
  --destructive-foreground: var(--white);
  --border: var(--orange);
  --input: var(--black);
  --ring: var(--orange-light);
}

/* Light theme overrides */
.light {
  --orange: #ff7f2a;
  --orange-light: #ff8800;
  --gray-dark: #e8e8e8;
  --gray-medium: #d0d0d0;
  --black: #f8f8f8;
  --white: #333;
  --accent: #222;

  --background: #eaeaea;
  --foreground: var(--white);
  --card: var(--gray-dark);
  --card-foreground: var(--white);
  --popover: var(--gray-dark);
  --popover-foreground: var(--white);
  --primary: var(--orange);
  --primary-foreground: var(--black);
  --secondary: var(--gray-medium);
  --secondary-foreground: var(--white);
  --muted: var(--gray-medium);
  --muted-foreground: var(--orange-light);
  --accent: var(--orange-light);
  --accent-foreground: var(--black);
  --border: var(--orange);
  --input: var(--black);
  --ring: var(--orange-light);
}

@theme inline {
  --radius-sm: 0;
  --radius-md: 0;
  --radius-lg: 0;
  --radius-xl: 0;
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-orange: var(--orange);
  --color-orange-light: var(--orange-light);
  --color-gray-dark: var(--gray-dark);
  --color-gray-medium: var(--gray-medium);
  --color-black: var(--black);
  --color-white: var(--white);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }

  body {
    @apply bg-background text-foreground;
    font-family: Consolas, Menlo, Monaco, "Liberation Mono", "Courier New", Courier, monospace;
    background: linear-gradient(120deg, var(--gray-dark) 0%, #181818 100%);
    min-height: 100vh;
    box-sizing: border-box;
    position: relative;
    overflow-x: hidden;
  }

  .light body {
    background: linear-gradient(135deg, #eaeaea 0%, #d8d8d8 100%);
  }

  /* Pixel art image rendering */
  img {
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
  }

  /* Screen reader only utility */
  .sr-only {
    position: absolute;
    width: 1px;
    height: 1px;
    padding: 0;
    margin: -1px;
    overflow: hidden;
    clip: rect(0, 0, 0, 0);
    white-space: nowrap;
    border: 0;
  }

  /* Section styling */
  .section {
    max-width: 900px;
    margin: 0 auto;
    padding: 4rem 1rem 3rem 1rem;
    border-bottom: 1px solid var(--gray-medium);
    background: var(--gray-dark);
    box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.08);
    border-radius: 0;
    margin-bottom: 2rem;
  }

  .section:last-child {
    border-bottom: none;
  }

  /* Custom color utilities */
  .text-orange { color: var(--orange); }
  .text-orange-light { color: var(--orange-light); }
  .text-gray-dark { color: var(--gray-dark); }
  .text-gray-medium { color: var(--gray-medium); }
  .text-black { color: var(--black); }
  .text-white { color: var(--white); }

  .bg-orange { background-color: var(--orange); }
  .bg-orange-light { background-color: var(--orange-light); }
  .bg-gray-dark { background-color: var(--gray-dark); }
  .bg-gray-medium { background-color: var(--gray-medium); }
  .bg-black { background-color: var(--black); }
  .bg-white { background-color: var(--white); }

  .border-orange { border-color: var(--orange); }
  .border-orange-light { border-color: var(--orange-light); }
  .border-gray-dark { border-color: var(--gray-dark); }
  .border-gray-medium { border-color: var(--gray-medium); }
  .border-black { border-color: var(--black); }
  .border-white { border-color: var(--white); }

  /* Responsive adjustments */
  @media (max-width: 900px) {
    .section {
      max-width: 98vw;
      padding: 2.5rem 0.5rem 2rem 0.5rem;
    }
  }

  /* Light theme adjustments */
  .light .section {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.06);
    border: 1px solid #ccc;
  }

  .light .navbar {
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  }
}
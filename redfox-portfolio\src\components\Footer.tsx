import { useEffect, useState } from 'react';

export function Footer() {
  const [currentDate, setCurrentDate] = useState('');

  useEffect(() => {
    const updateDateTime = () => {
      const now = new Date();
      const dateStr = now.toLocaleDateString('fr-FR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
      setCurrentDate(dateStr);
    };

    updateDateTime();
    const interval = setInterval(updateDateTime, 1000);

    return () => clearInterval(interval);
  }, []);

  return (
    <footer className="text-center px-4 py-4 text-gray-medium text-xs bg-black border-t border-gray-medium mt-8">
      <p>&copy; 2025 RedFox. Tous droits réservés.</p>
      <p className="mt-2 font-mono text-gray-medium opacity-80">
        {currentDate}
      </p>
    </footer>
  );
}

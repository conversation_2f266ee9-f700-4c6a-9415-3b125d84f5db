import { useState, useRef, useEffect } from 'react';
import { Button } from './ui/button';

interface Message {
  role: 'user' | 'assistant';
  content: string;
}

export function Chatbot() {
  const [isOpen, setIsOpen] = useState(false);
  const [messages, setMessages] = useState<Message[]>([
    { role: 'assistant', content: 'Bon<PERSON><PERSON>, je suis à votre disposition pour répondre à vos questions sur RedFox.' }
  ]);
  const [inputValue, setInputValue] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  const handleToggle = () => {
    setIsOpen(!isOpen);
  };

  const handleClear = () => {
    setMessages([
      { role: 'assistant', content: 'Bonjour, je suis à votre disposition pour répondre à vos questions sur RedFox.' }
    ]);
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage = inputValue.trim();
    setInputValue('');
    
    // Add user message
    const newMessages = [...messages, { role: 'user' as const, content: userMessage }];
    setMessages(newMessages);
    setIsLoading(true);

    try {
      // Simulate API call - replace with actual chatbot integration
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Simple mock responses
      const mockResponses = [
        "Merci pour votre question ! Je suis spécialisé en IA générative et développement fullstack.",
        "C'est une excellente question ! N'hésitez pas à me contacter pour plus d'informations.",
        "Je serais ravi de discuter de vos projets d'IA générative avec vous.",
        "Mes compétences incluent Python, JavaScript, et bien sûr l'IA générative !"
      ];
      
      const response = mockResponses[Math.floor(Math.random() * mockResponses.length)];
      
      setMessages(prev => [...prev, { role: 'assistant', content: response }]);
    } catch (error) {
      setMessages(prev => [...prev, { 
        role: 'assistant', 
        content: 'Désolé, une erreur s\'est produite. Veuillez réessayer.' 
      }]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      handleSendMessage();
    }
  };

  return (
    <div className="fixed bottom-5 right-5 z-50">
      {/* Toggle Button */}
      <Button
        onClick={handleToggle}
        className={`
          bg-orange text-black border-2 border-orange-light rounded-none w-12 h-12 text-2xl
          cursor-pointer shadow-[0_2px_0_var(--black)] transition-all duration-200
          flex justify-center items-center hover:bg-orange-light hover:scale-105
          ${isOpen ? 'hidden' : 'flex'}
        `}
      >
        💬
      </Button>

      {/* Chat Window */}
      <div className={`
        w-80 h-96 bg-gray-dark border-4 border-orange rounded-none
        shadow-[0_4px_10px_rgba(0,0,0,0.4)] flex flex-col overflow-hidden
        transition-all duration-400 ease-out
        ${isOpen 
          ? 'opacity-100 scale-100 translate-y-0' 
          : 'opacity-0 scale-90 translate-y-2 pointer-events-none'
        }
      `}>
        {/* Header */}
        <div className="bg-orange text-black px-3 py-2 font-bold flex justify-between items-center border-b-2 border-orange-light text-lg">
          <span>Interrogez-moi !</span>
          <div className="flex items-center gap-1">
            <Button
              onClick={handleClear}
              className="bg-transparent border-none text-black text-xl cursor-pointer px-1 hover:text-gray-dark"
              title="Effacer le chat"
            >
              🗑️
            </Button>
            <Button
              onClick={handleToggle}
              className="bg-transparent border-none text-black text-2xl cursor-pointer px-1 hover:text-gray-dark"
              title="Fermer"
            >
              ×
            </Button>
          </div>
        </div>

        {/* Messages */}
        <div className="flex-grow px-2 py-2 overflow-y-auto bg-gray-medium flex flex-col gap-2 border-b-2 border-orange">
          {messages.map((message, index) => (
            <div
              key={index}
              className={`
                px-3 py-2 rounded max-w-[85%] break-words text-sm leading-relaxed
                shadow-[1px_1px_0px_rgba(0,0,0,0.2)]
                ${message.role === 'assistant'
                  ? 'bg-white text-black self-start border border-gray-dark'
                  : 'bg-orange-light text-black self-end border border-orange'
                }
              `}
            >
              {message.content}
            </div>
          ))}
          
          {isLoading && (
            <div className="bg-white text-black self-start border border-gray-dark px-3 py-2 rounded max-w-[85%] text-sm">
              Je réfléchis...
            </div>
          )}
          
          <div ref={messagesEndRef} />
        </div>

        {/* Input */}
        <div className="flex p-2 bg-gray-dark">
          <input
            type="text"
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Votre message..."
            className="flex-grow px-2 py-2 border-2 border-orange-light rounded-none mr-2 bg-black text-white font-mono text-base outline-none focus:border-white"
            disabled={isLoading}
          />
          <Button
            onClick={handleSendMessage}
            disabled={isLoading || !inputValue.trim()}
            className="px-4 py-2 bg-orange text-black border-2 border-orange-light rounded-none cursor-pointer transition-all duration-200 font-mono font-bold text-base hover:bg-orange-light hover:border-white disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Envoyer
          </Button>
        </div>
      </div>
    </div>
  );
}

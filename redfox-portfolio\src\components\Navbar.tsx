import { Button } from './ui/button';

interface NavbarProps {
  onSidebarToggle: () => void;
  onThemeToggle: () => void;
  isLightTheme: boolean;
  isSidebarOpen: boolean;
}

export function Navbar({ onSidebarToggle, onThemeToggle, isLightTheme, isSidebarOpen }: NavbarProps) {
  const handleSidebarToggle = () => {
    onSidebarToggle();
  };

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId);
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' });
    }
  };

  return (
    <nav className="sticky top-0 bg-black z-50 border-b-4 border-orange shadow-[0_2px_0_var(--gray-medium)]">
      <div className="flex items-center justify-start">
        <Button
          variant="ghost"
          size="sm"
          onClick={handleSidebarToggle}
          className={`
            w-11 h-11 p-0 m-2 bg-transparent text-orange border-none rounded-none
            hover:bg-orange hover:text-black transition-colors duration-200
            flex items-center justify-center relative z-30
            ${isSidebarOpen ? 'bg-orange text-black' : ''}
          `}
          aria-label="Ouvrir/fermer le menu"
          aria-expanded={isSidebarOpen}
        >
          <div className="flex flex-col justify-center items-center w-6 h-4 relative">
            <span 
              className={`
                block w-full h-0.5 bg-current mb-1 rounded-sm transition-all duration-300 ease-in-out
                ${isSidebarOpen ? 'transform translate-y-1.5 rotate-45' : ''}
              `}
            />
            <span 
              className={`
                block w-full h-0.5 bg-current mb-1 rounded-sm transition-all duration-300 ease-in-out
                ${isSidebarOpen ? 'opacity-0' : ''}
              `}
            />
            <span 
              className={`
                block w-full h-0.5 bg-current rounded-sm transition-all duration-300 ease-in-out
                ${isSidebarOpen ? 'transform -translate-y-1.5 -rotate-45' : ''}
              `}
            />
          </div>
        </Button>

        <ul className="flex-1 flex justify-center gap-8 list-none m-0 py-2 ml-2">
          <li>
            <button
              onClick={() => scrollToSection('accueil')}
              className="text-orange font-bold text-lg px-3 py-1 rounded-none bg-transparent border-none hover:bg-orange hover:text-black transition-colors duration-200"
            >
              Accueil
            </button>
          </li>
          <li>
            <button
              onClick={() => scrollToSection('a-propos')}
              className="text-orange font-bold text-lg px-3 py-1 rounded-none bg-transparent border-none hover:bg-orange hover:text-black transition-colors duration-200"
            >
              À propos
            </button>
          </li>
          <li>
            <button
              onClick={() => scrollToSection('competences')}
              className="text-orange font-bold text-lg px-3 py-1 rounded-none bg-transparent border-none hover:bg-orange hover:text-black transition-colors duration-200"
            >
              Compétences
            </button>
          </li>
          <li>
            <button
              onClick={() => scrollToSection('projets')}
              className="text-orange font-bold text-lg px-3 py-1 rounded-none bg-transparent border-none hover:bg-orange hover:text-black transition-colors duration-200"
            >
              Projets
            </button>
          </li>
          <li>
            <button
              onClick={() => scrollToSection('certifications')}
              className="text-orange font-bold text-lg px-3 py-1 rounded-none bg-transparent border-none hover:bg-orange hover:text-black transition-colors duration-200"
            >
              Certifications
            </button>
          </li>
          <li>
            <button
              onClick={() => scrollToSection('contact')}
              className="text-orange font-bold text-lg px-3 py-1 rounded-none bg-transparent border-none hover:bg-orange hover:text-black transition-colors duration-200"
            >
              Contact
            </button>
          </li>
        </ul>

        <Button
          variant="ghost"
          size="sm"
          onClick={onThemeToggle}
          className="text-orange bg-transparent border-none rounded-none px-3 py-1 mr-2 hover:bg-orange hover:text-black transition-colors duration-200"
          aria-label="Changer de thème"
          title="Changer de thème"
        >
          <span className="text-lg">{isLightTheme ? '☀️' : '🌙'}</span>
        </Button>
      </div>
    </nav>
  );
}

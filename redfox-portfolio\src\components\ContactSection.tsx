import { useState } from 'react';
import { Button } from './ui/button';

export function ContactSection() {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitText, setSubmitText] = useState('Envoyer');

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setSubmitText('Envoyé !');

    // Animate fox mascot (simple scale animation)
    const foxElement = document.querySelector('.fox-mascot-contact');
    if (foxElement) {
      foxElement.animate([
        { transform: 'translateY(0)' },
        { transform: 'translateY(-18px)' },
        { transform: 'translateY(0)' }
      ], {
        duration: 1000,
        easing: 'cubic-bezier(0.4,1.4,0.6,1)'
      });
    }

    // Reset form after animation
    setTimeout(() => {
      setSubmitText('Envoyer');
      setFormData({ name: '', email: '', message: '' });
      setIsSubmitting(false);
    }, 1800);
  };

  return (
    <section id="contact" className="section">
      <h2 className="text-2xl text-white mb-2">Contact</h2>
      
      <form 
        className="flex flex-col gap-3 max-w-sm mx-auto mt-6 bg-gray-medium px-4 py-5 rounded-none shadow-[0_2px_0_var(--black)] border-2 border-orange"
        onSubmit={handleSubmit}
        autoComplete="off"
      >
        <label htmlFor="name" className="text-orange-light font-bold text-base">
          Nom
        </label>
        <input
          type="text"
          id="name"
          name="name"
          value={formData.name}
          onChange={handleInputChange}
          required
          className="font-mono text-base border-2 border-gray-dark rounded-none px-3 py-2 bg-black text-white outline-none transition-colors duration-200 resize-none focus:border-orange-light"
        />
        
        <label htmlFor="email" className="text-orange-light font-bold text-base">
          Email
        </label>
        <input
          type="email"
          id="email"
          name="email"
          value={formData.email}
          onChange={handleInputChange}
          required
          className="font-mono text-base border-2 border-gray-dark rounded-none px-3 py-2 bg-black text-white outline-none transition-colors duration-200 resize-none focus:border-orange-light"
        />
        
        <label htmlFor="message" className="text-orange-light font-bold text-base">
          Message
        </label>
        <textarea
          id="message"
          name="message"
          rows={3}
          value={formData.message}
          onChange={handleInputChange}
          required
          className="font-mono text-base border-2 border-gray-dark rounded-none px-3 py-2 bg-black text-white outline-none transition-colors duration-200 resize-none focus:border-orange-light"
        />
        
        <Button
          type="submit"
          disabled={isSubmitting}
          className="bg-orange text-black font-bold border-2 border-orange-light rounded-none px-5 py-2 text-lg cursor-pointer shadow-none transition-all duration-200 hover:bg-orange-light hover:text-white hover:border-white focus:bg-orange-light focus:text-white focus:border-white"
        >
          {submitText}
        </Button>
      </form>

      <div className="flex justify-center gap-6 mt-5">
        <a
          href="mailto:<EMAIL>"
          className="text-orange bg-gray-dark border-2 border-orange-light rounded-none px-4 py-2 no-underline font-bold transition-all duration-200 shadow-none hover:bg-orange hover:text-black hover:border-white focus:bg-orange focus:text-black focus:border-white"
        >
          Email
        </a>
        <a
          href="#"
          className="text-orange bg-gray-dark border-2 border-orange-light rounded-none px-4 py-2 no-underline font-bold transition-all duration-200 shadow-none hover:bg-orange hover:text-black hover:border-white focus:bg-orange focus:text-black focus:border-white"
        >
          GitHub
        </a>
        <a
          href="#"
          className="text-orange bg-gray-dark border-2 border-orange-light rounded-none px-4 py-2 no-underline font-bold transition-all duration-200 shadow-none hover:bg-orange hover:text-black hover:border-white focus:bg-orange focus:text-black focus:border-white"
        >
          LinkedIn
        </a>
      </div>

      <div className="flex justify-center mt-6">
        <img
          src="/images/fox_transparent.png"
          alt="Logo RedFox"
          className="fox-mascot-contact max-w-[80px] fox-blink"
        />
      </div>
    </section>
  );
}

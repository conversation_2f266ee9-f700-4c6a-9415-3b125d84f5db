import { useEffect, useRef, useState } from 'react';

interface SidebarProps {
  isOpen: boolean;
  onClose: () => void;
}

interface Definition {
  term: string;
  description: string;
}

const definitions: Definition[] = [
  {
    term: "IA Générative",
    description: "Technologie d'intelligence artificielle capable de créer du contenu original (texte, images, sons) en s'inspirant de données existantes. Exemples : ChatGPT, DALL-E, Midjourney."
  },
  {
    term: "Machine Learning",
    description: "Branche de l'IA permettant aux ordinateurs d'apprendre à partir de données sans être explicitement programmés. Fondement des systèmes prédictifs et de reconnaissance de motifs."
  },
  {
    term: "Développeur Fullstack",
    description: "Professionnel maîtrisant à la fois le développement frontend (interface utilisateur) et backend (serveur, base de données), capable de créer des applications web complètes."
  },
  {
    term: "Intégrateur Cloud",
    description: "Expert qui implémente, configure et optimise les solutions cloud pour les entreprises, assurant la migration et l'interconnexion des services."
  },
  {
    term: "Pixel Art",
    description: "Style graphique numérique où les images sont créées au niveau du pixel, souvent avec une palette de couleurs limitée, évoquant l'esthétique des jeux vidéo rétro."
  },
  {
    term: "Azure",
    description: "Plateforme cloud de Microsoft offrant des services d'hébergement, de calcul, d'IA et d'analyse de données pour les entreprises et développeurs."
  },
  {
    term: "AZ-900",
    description: "Certification Microsoft Azure Fundamentals qui valide les connaissances de base sur les services cloud, les modèles de tarification et la conformité dans Azure."
  }
];

export function Sidebar({ isOpen, onClose }: SidebarProps) {
  const [animatedContent, setAnimatedContent] = useState<{ [key: string]: string }>({});
  const sidebarRef = useRef<HTMLDivElement>(null);

  const typewriterEffect = (text: string, speed: number = 30): Promise<void> => {
    return new Promise((resolve) => {
      let i = 0;
      const interval = setInterval(() => {
        if (i <= text.length) {
          setAnimatedContent(prev => ({
            ...prev,
            [text]: text.substring(0, i)
          }));
          i++;
        } else {
          clearInterval(interval);
          resolve();
        }
      }, speed);
    });
  };

  useEffect(() => {
    if (isOpen) {
      // Reset animated content
      setAnimatedContent({});
      
      // Start typewriter animation
      const animateContent = async () => {
        // Animate title first
        await typewriterEffect("Définitions", 50);
        
        // Animate each definition with progressive delays
        for (let i = 0; i < definitions.length; i++) {
          const def = definitions[i];
          await new Promise(resolve => setTimeout(resolve, 300)); // Delay between definitions
          await typewriterEffect(def.term, 40);
          await new Promise(resolve => setTimeout(resolve, 100));
          await typewriterEffect(def.description, 25);
        }
      };
      
      setTimeout(animateContent, 200);
    }
  }, [isOpen]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (sidebarRef.current && !sidebarRef.current.contains(event.target as Node)) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [isOpen, onClose]);

  return (
    <aside
      ref={sidebarRef}
      className={`
        fixed left-0 top-0 h-full w-64 bg-gray-medium border-r-4 border-orange
        shadow-[2px_0_5px_rgba(0,0,0,0.2)] p-4 pt-16 z-30 overflow-y-auto
        transition-transform duration-300 ease-in-out
        ${isOpen ? 'transform-none' : '-translate-x-full'}
      `}
      id="sidebar-definitions"
    >
      <h2 className="text-orange-light mb-6 text-xl text-center">
        {animatedContent["Définitions"] || ""}
      </h2>
      
      <ul className="list-none p-0 m-0">
        {definitions.map((def, index) => (
          <li key={index} className="mb-4 text-white text-sm">
            <strong className="text-orange block mb-1">
              {animatedContent[def.term] || ""}
            </strong>
            <span>
              {animatedContent[def.description] || ""}
            </span>
          </li>
        ))}
      </ul>
    </aside>
  );
}
